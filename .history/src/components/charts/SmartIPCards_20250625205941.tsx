import React, { useState } from 'react';

interface IPInfo {
  ip: string;
  count: number;
  country?: string;
  countryCode?: string;
  location?: string;
  threatLevel?: 'low' | 'medium' | 'high' | 'critical';
  threatInfo?: string;
  isp?: string;
}

interface SmartIPCardsProps {
  data: IPInfo[];
  baseColor?: string;
  showDetails?: boolean;
}

// 简化版的国旗数据 - 正式环境建议使用国旗图标库或API
const countryFlagEmojis: Record<string, string> = {
  'CN': '🇨🇳',
  'US': '🇺🇸',
  'JP': '🇯🇵',
  'KR': '🇰🇷',
  'RU': '🇷🇺',
  'GB': '🇬🇧',
  'DE': '🇩🇪',
  'FR': '🇫🇷',
  'CA': '🇨🇦',
  'AU': '🇦🇺',
  // 可根据需要扩充
};

// 威胁等级对应的颜色
const threatColors = {
  'low': '#4ade80',
  'medium': '#facc15',
  'high': '#fb923c',
  'critical': '#ef4444',
};

const SmartIPCards: React.FC<SmartIPCardsProps> = ({ 
  data, 
  baseColor = '#00d9ff',
  showDetails = true
}) => {
  const sortedData = [...data].sort((a, b) => b.count - a.count).slice(0, 2);
  const maxValue = Math.max(...sortedData.map(item => item.count));
  const [expandedIndex, setExpandedIndex] = useState<number | null>(null);
  
  return (
    <div className="grid gap-0.5">
      {sortedData.map((item, index) => {
        const flag = item.countryCode ? countryFlagEmojis[item.countryCode] || '' : '';
        const isExpanded = expandedIndex === index;
        const percentage = Math.max((item.count / maxValue) * 100, 15);
        
        return (
          <div
            key={item.ip}
            className={`relative rounded-lg transition-all duration-300 overflow-hidden bg-[#0a1629]/90 border ${index === 0 ? 'border-[#00d9ff]/60' : 'border-[#00d9ff]/30'}`}
            style={{
              height: isExpanded ? 'auto' : '48px',
              boxShadow: index === 0 ? `0 0 12px 0px ${baseColor}40` : `0 0 6px 0px ${baseColor}20`,
            }}
            onClick={() => setExpandedIndex(isExpanded ? null : index)}
          >
            {/* 进度条背景，表示攻击量占比 */}
            <div
              className="absolute left-0 top-0 h-full bg-[#0e2b45]/60 z-0 transition-all duration-500"
              style={{ width: `${percentage}%` }}
            />

            {/* 内容区 */}
            <div className="relative z-10 px-3 py-2 flex items-center">
              {/* 威胁等级指示点 */}
              <div
                className="w-3 h-3 rounded-full mr-3 shadow-lg"
                style={{
                  backgroundColor: threatColors[item.threatLevel || 'medium'],
                  boxShadow: `0 0 8px ${threatColors[item.threatLevel || 'medium']}60`
                }}
              />

              {/* IP地址与国旗 */}
              <div className="flex items-center flex-1 space-x-2">
                <span className="font-mono text-sm font-semibold text-white">{item.ip}</span>
                <span className="text-base">{flag}</span>

                <div className="flex-1" />

                <span className="text-sm px-2 py-1 rounded-md bg-[#0c1c2d]/80 text-[#e0f2fe] font-medium border border-[#334155]/50">
                  {item.count.toLocaleString()}
                </span>

                <div className="ml-2">
                  <svg
                    className={`w-5 h-5 text-[#94a3b8] transition-transform ${isExpanded ? 'transform rotate-180' : ''}`}
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
            
            {/* 展开后显示的详情 */}
            {isExpanded && showDetails && (
              <div className="px-3 pb-3 text-sm border-t border-[#334155]/50 bg-[#0f172a]/50">
                <div className="mt-2 flex flex-wrap gap-4">
                  <div className="flex items-center">
                    <span
                      className="inline-block w-2 h-2 mr-2 rounded-full shadow-sm"
                      style={{
                        backgroundColor: threatColors[item.threatLevel || 'medium'],
                        boxShadow: `0 0 4px ${threatColors[item.threatLevel || 'medium']}60`
                      }}
                    ></span>
                    <span className="text-[#b6c7d8]">威胁等级:</span>
                    <span
                      className="ml-2 font-medium"
                      style={{ color: threatColors[item.threatLevel || 'medium'] }}
                    >
                      {item.threatLevel === 'critical' ? '严重' :
                        item.threatLevel === 'high' ? '高危' :
                        item.threatLevel === 'medium' ? '中等' : '低'}
                    </span>
                  </div>

                  <div className="flex items-center">
                    <span className="text-[#b6c7d8]">攻击次数:</span>
                    <span className="ml-2 text-white font-semibold">{item.count.toLocaleString()}</span>
                    <span className="ml-2 text-[#94a3b8]">(占比 {Math.round(percentage)}%)</span>
                  </div>

                  <div className="flex items-center">
                    <span className="text-[#b6c7d8]">攻击类型:</span>
                    <span className="ml-2 text-[#fbbf24]">{item.threatInfo || 'DDoS攻击'}</span>
                  </div>
                </div>
                
                <div className="flex flex-wrap mt-3 gap-4 text-sm">
                  <div className="flex items-center">
                    <span className="text-[#b6c7d8]">国家/地区:</span>
                    <span className="ml-2 text-white font-medium">{flag} {item.country || '未知地区'}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-[#b6c7d8]">位置:</span>
                    <span className="ml-2 text-white">{item.location || '未知城市'}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-[#b6c7d8]">服务商:</span>
                    <span className="ml-2 text-white">{item.isp || '未知ISP'}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-[#b6c7d8]">最后活动:</span>
                    <span className="ml-2 text-[#fbbf24]">实时监控中</span>
                  </div>
                </div>

                <div className="mt-3 flex gap-2 text-sm">
                  <button className="text-[#00d9ff] hover:text-[#38bdf8] px-3 py-1.5 bg-[#0f172a]/80 border border-[#00d9ff]/30 rounded-md transition-colors font-medium">
                    加入黑名单
                  </button>
                  <button className="text-[#00d9ff] hover:text-[#38bdf8] px-3 py-1.5 bg-[#0f172a]/80 border border-[#00d9ff]/30 rounded-md transition-colors font-medium">
                    查看详情
                  </button>
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default SmartIPCards;
